'use client';

import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Pagination from '@mui/material/Pagination';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { TrashIcon } from '@phosphor-icons/react/dist/ssr/Trash';

import { Role } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';

import { InviteMemberDialog } from './invite-member-dialog';
import { User, WorkspaceInvite, WorkspaceMembership } from '@prisma/client';

type WorkspaceMemberWithDetails = WorkspaceMembership & {
  user: User;
  role: Role;
};

type WorkspaceInviteWithDetails = WorkspaceInvite & {
  invitedByUser: User;
  user: User | null;
};

export function TeamSettingsClient(): React.JSX.Element {
  const t = useTranslations('settings.team');
  const { currentWorkspace } = useWorkspace();
  const { workspaceApiService } = useApiServices();

  // State for members and invitations
  const [members, setMembers] = React.useState<WorkspaceMemberWithDetails[]>([]);
  const [invitations, setInvitations] = React.useState<WorkspaceInviteWithDetails[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [membersLoading, setMembersLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  // Pagination and search state
  const [currentPage, setCurrentPage] = React.useState(1);
  const [totalPages, setTotalPages] = React.useState(1);
  const [totalMembers, setTotalMembers] = React.useState(0);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState('');

  // Invite dialog state
  const [inviteDialogOpen, setInviteDialogOpen] = React.useState(false);
  const [roles, setRoles] = React.useState<Role[]>([]);
  const [rolesLoading, setRolesLoading] = React.useState(false);

  // Constants
  const MEMBERS_PER_PAGE = 10;

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery]);

  // Load roles
  const loadRoles = React.useCallback(async () => {
    if (!currentWorkspace) return;

    try {
      setRolesLoading(true);
      const rolesData = await workspaceApiService.getWorkspaceRoles(currentWorkspace.id);
      setRoles(rolesData);
    } catch (err) {
      console.error('Error loading roles:', err);
    } finally {
      setRolesLoading(false);
    }
  }, [currentWorkspace, workspaceApiService]);

  // Load members (for pagination and search)
  const loadMembers = React.useCallback(async () => {
    if (!currentWorkspace) return;

    try {
      setMembersLoading(true);
      setError(null);

      const membersResponse = await workspaceApiService.getWorkspaceMembers(currentWorkspace.id, {
        page: currentPage,
        limit: MEMBERS_PER_PAGE,
        search: debouncedSearchQuery || undefined,
      });

      setMembers(membersResponse.data);
      setTotalPages(membersResponse.pagination.totalPages);
      setTotalMembers(membersResponse.pagination.total);
    } catch (err) {
      console.error('Error loading members:', err);
      setError('Failed to load members');
    } finally {
      setMembersLoading(false);
    }
  }, [currentWorkspace, workspaceApiService, currentPage, debouncedSearchQuery]);

  // Load invitations
  const loadInvitations = React.useCallback(async () => {
    if (!currentWorkspace) return;

    try {
      const invitationsData = await workspaceApiService.getWorkspaceInvitations(currentWorkspace.id);
      setInvitations(invitationsData.filter((inv) => inv.status === 'PENDING'));
    } catch (err) {
      console.error('Error loading invitations:', err);
    }
  }, [currentWorkspace, workspaceApiService]);

  // Initial load - runs once when workspace changes
  React.useEffect(() => {
    if (!currentWorkspace) return;

    const loadInitialData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load all data in parallel
        await Promise.all([
          loadMembers(),
          loadInvitations(),
          loadRoles(),
        ]);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError('Failed to load team data');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, [currentWorkspace?.id]); // Only depend on workspace ID to avoid recreation loops

  // Load members when search or pagination changes (but not on initial load)
  React.useEffect(() => {
    if (!currentWorkspace || loading) return; // Skip if initial loading

    loadMembers();
  }, [loadMembers, currentPage, debouncedSearchQuery, currentWorkspace, loading]); // Include all dependencies

  // Handle invitation cancellation
  const handleCancelInvitation = async (inviteId: string) => {
    try {
      await workspaceApiService.cancelInvitation(inviteId);
      // Reload invitations to reflect changes
      await loadInvitations();
    } catch (err) {
      console.error('Error cancelling invitation:', err);
      setError('Failed to cancel invitation');
    }
  };

  // Handle invite member
  const handleInviteMember = () => {
    setInviteDialogOpen(true);
  };

  // Handle sending invitation
  const handleSendInvitation = async (email: string, roleId: string) => {
    if (!currentWorkspace) return;

    try {
      await workspaceApiService.createInvitation(currentWorkspace.id, email, roleId);
      // Reload invitations to show the new invitation
      await loadInvitations();
    } catch (err) {
      console.error('Error sending invitation:', err);
      throw err; // Re-throw to let the dialog handle the error
    }
  };

  // Skeleton loading component for table rows
  const TableSkeleton = () => (
    <>
      {Array.from({ length: 2 }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Stack direction="row" spacing={2} alignItems="center">
              <Skeleton variant="circular" width={40} height={40} />
              <Skeleton variant="text" width={120} height={20} />
            </Stack>
          </TableCell>
          <TableCell>
            <Skeleton variant="text" width={180} height={20} />
          </TableCell>
          <TableCell>
            <Skeleton variant="rectangular" width={80} height={24} sx={{ borderRadius: 1 }} />
          </TableCell>
          <TableCell align="right">
            <Skeleton variant="rectangular" width={60} height={32} sx={{ borderRadius: 1 }} />
          </TableCell>
        </TableRow>
      ))}
    </>
  );

  if (!currentWorkspace) {
    return (
      <Stack spacing={3}>
        <Typography variant='h6'>{t('title')}</Typography>
        <Typography color='text.secondary'>No workspace selected</Typography>
      </Stack>
    );
  }



  if (error) {
    const handleRetry = async () => {
      try {
        setLoading(true);
        setError(null);
        await Promise.all([
          loadMembers(),
          loadInvitations(),
          loadRoles(),
        ]);
      } catch (err) {
        console.error('Error retrying:', err);
        setError('Failed to load team data');
      } finally {
        setLoading(false);
      }
    };

    return (
      <Stack spacing={3}>
        <Stack direction='row' spacing={3} alignItems='center' justifyContent='space-between'>
          <Typography variant='h6'>{t('title')}</Typography>
          <Button variant='contained' startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />} disabled>
            {t('inviteMember')}
          </Button>
        </Stack>
        <Typography color='error'>{error}</Typography>
        <Button onClick={handleRetry}>Retry</Button>
      </Stack>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      <Stack spacing={3}>
        {/* Search Bar */}
        <TextField
          placeholder={t('searchPlaceholder')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position='start'>
                  <MagnifyingGlassIcon />
                </InputAdornment>
              ),
            },
          }}
          sx={{
            width: { xs: '100%', sm: 400 },
            maxWidth: '100%'
          }}
        />

        {/* Current Members */}
        <Card>
          <CardHeader
            title={t('members')}
            action={
              <Button
                variant='contained'
                size='medium'
                startIcon={<PlusIcon fontSize='var(--icon-fontSize-sm)' />}
                onClick={handleInviteMember}
                sx={{
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none'
                  },
                  '&:active': {
                    boxShadow: 'none'
                  },
                  minWidth: { xs: 'auto', sm: 'auto' },
                  px: { xs: 1.5, sm: 2 },
                  '& .MuiButton-startIcon': {
                    display: { xs: 'none', sm: 'inline-flex' },
                    mr: { xs: 0, sm: 1 }
                  }
                }}
              >
                <Box sx={{ display: { xs: 'none', sm: 'inline' } }}>
                  {t('inviteMember')}
                </Box>
                <Box sx={{ display: { xs: 'flex', sm: 'none' }, alignItems: 'center' }}>
                  <PlusIcon fontSize='var(--icon-fontSize-md)' />
                </Box>
              </Button>
            }
            sx={{
              py: 2,
              '& .MuiCardHeader-action': {
                alignSelf: 'center',
                mt: 0,
                mr: 0
              }
            }}
          />
          <Divider />
          <Box sx={{ overflowX: 'auto' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell>{t('name')}</TableCell>
                  <TableCell>{t('email')}</TableCell>
                  <TableCell>{t('role')}</TableCell>
                  <TableCell align='right'>{t('actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading || membersLoading ? (
                  <TableSkeleton />
                ) : members.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                        {debouncedSearchQuery
                          ? t('noMembersMatchSearch', { query: debouncedSearchQuery })
                          : t('noMembers')
                        }
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  members.map((member) => (
                    <TableRow key={member.userId} hover>
                      <TableCell>
                        <Stack direction='row' spacing={2} alignItems='center'>
                          <Avatar src={member.user.avatar || undefined}>
                            {member.user.displayName?.charAt(0) || member.user.email?.charAt(0) || 'U'}
                          </Avatar>
                          <Typography variant='body1'>
                            {member.user.displayName || member.user.email || 'Unknown User'}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>{member.user.email}</TableCell>
                      <TableCell>
                        <Chip
                          label={member.role.name || member.role.id}
                          size='small'
                          color={member.role.id === 'owner' ? 'primary' : 'default'}
                        />
                      </TableCell>
                      <TableCell align='right'>
                        <Button variant='text' color='primary' size='small'>
                          {t('edit')}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Box>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(_, page) => setCurrentPage(page)}
                color='primary'
              />
            </Box>
          )}
        </Card>

        {/* Pending Invitations */}
        {invitations.length > 0 && (
          <Card>
            <CardHeader title={t('pendingInvitations')} />
            <Divider />
            <Box sx={{ overflowX: 'auto' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('email')}</TableCell>
                    <TableCell>Invited By</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell align='right'>{t('actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invitations.map((invitation) => (
                    <TableRow key={invitation.id} hover>
                      <TableCell>{invitation.email}</TableCell>
                      <TableCell>
                        {invitation.invitedByUser?.displayName || invitation.invitedByUser?.email || 'Unknown'}
                      </TableCell>
                      <TableCell>{new Date(invitation.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell align='right'>
                        <IconButton color='error' size='small' onClick={() => handleCancelInvitation(invitation.id)}>
                          <TrashIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Card>
        )}

        {invitations.length === 0 && (
          <Card>
            <CardHeader title={t('pendingInvitations')} />
            <Divider />
            <CardContent>
              <Typography color='text.secondary' align='center'>
                {t('noPendingInvitations')}
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Invite Member Dialog */}
        <InviteMemberDialog
          open={inviteDialogOpen}
          onClose={() => setInviteDialogOpen(false)}
          onInvite={handleSendInvitation}
          roles={roles}
          loading={rolesLoading}
        />
      </Stack>
    </Box>
  );
}
